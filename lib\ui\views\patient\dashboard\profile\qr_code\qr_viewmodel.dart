import 'dart:developer';
import 'dart:io';
import 'dart:typed_data';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:screenshot/screenshot.dart';
import 'package:stacked/stacked.dart';
import 'package:track_med_rec/app/app.locator.dart';
import 'package:track_med_rec/core/network/models/user_model.dart';
import 'package:track_med_rec/core/service/service.dart';
import 'package:track_med_rec/core/service/services/profile_service.dart';

class QrViewModel extends ReactiveViewModel {
  final ProfileService _profileService = locator<ProfileService>();
  final _flushBarService = locator<FlushBarService>();

  UserModel? get user =>
      _profileService.user.value ??
      locator<AuthService>().user ??
      locator<HiveDataBaseService>().getUser();
  bool isCaptured = false;

  Future<void> captureAndSharePng(ScreenshotController controller) async {
    _requestWritePermission();
    controller.capture().then((imageData) async {
      String path = '';
      final directory = await getApplicationCacheDirectory();
      if (Platform.isAndroid) {
        path = '/storage/emulated/0/Download/';
      } else {
        final downloadDir = await getDownloadsDirectory();
        path = downloadDir?.path ?? directory.path;
      }
      final file = File(path += '/PatientQRCode.jpg');
      await file.writeAsBytes(imageData!);
      await _flushBarService.showFlushSuccess(
          title: "Lab test saved successfully");
    }).onError((error, stackTrace) {
      log(error.toString());
    });
  }

  Future<void> saveImageToGallery(Uint8List? imageData) async {
    _requestWritePermission();
    if (imageData != null) {
      final directory = await getDownloadsDirectory();
      final galleryPath = '${directory!.path}/Pictures';

      // Create the Pictures directory if it doesn't exist
      await Directory(galleryPath).create(recursive: true);

      // Generate a unique filename
      final fileName = '${DateTime.now().millisecondsSinceEpoch}.jpg';

      // Create a new file and write the image data to it
      final File newImage = File('$galleryPath/$fileName');
      await newImage.writeAsBytes(imageData);

      // Refresh the gallery so the new image appears
      // await newImage.parent.refresh();

      log('Image saved to gallery: ${newImage.path}');
    }
  }

  Future<bool> _requestWritePermission() async {
    await Permission.storage.request();
    return await Permission.storage.request().isGranted;
  }

  Future<void> download(Uint8List? imageData) async {
    if (imageData != null) {
      bool hasPermission = await _requestWritePermission();
      if (!hasPermission) return;

      var dir = Platform.isAndroid
          ? await getExternalStorageDirectory()
          : await getApplicationDocumentsDirectory();

      // You should put the name you want for the file here.
      // Take in account the extension.
      final fileName = '${DateTime.now().millisecondsSinceEpoch}.jpg';
      File outputFile = File(Platform.isAndroid
          ? "/sdcard/download/$fileName"
          : "${dir!.path}/$fileName");
      await outputFile.writeAsBytes(imageData);
      _flushBarService.showFlushSuccess(
          title: 'Check your gallery for the qrcode and number');
      Future.delayed(const Duration(seconds: 2));
      log('Image saved to pictures: ${outputFile.path}');
    }
  }

  Future<void> saveImageToGallery2(Uint8List? imageData) async {
    if (imageData != null) {
      await Permission.storage.request().then((value) async {
        if (value == PermissionStatus.granted) {
          try {
            // Save to Downloads directory as a fallback
            final directory = await getDownloadsDirectory();
            if (directory != null) {
              final file = File(
                  '${directory.path}/PatientQRCode_${DateTime.now().millisecondsSinceEpoch}.jpg');
              await file.writeAsBytes(imageData);
              log('Image saved to downloads: ${file.path}');
              _flushBarService.showFlushSuccess(
                  title: "QR Code saved successfully");
            }
          } catch (e) {
            log('Error saving image: $e');
            _flushBarService.showFlushError(title: "Failed to save QR Code");
          }
        }
      });
    }
  }

  @override
  List<ListenableServiceMixin> get listenableServices => [_profileService];
}
