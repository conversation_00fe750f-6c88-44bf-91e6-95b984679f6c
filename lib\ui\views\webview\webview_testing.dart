// ignore_for_file: depend_on_referenced_packages

import 'dart:async';
import 'dart:core';

import 'package:flutter/material.dart';
import 'package:stacked/stacked.dart';
import 'package:webview_flutter/webview_flutter.dart';

import 'package:track_med_rec/ui/views/webview/webview_testing_viewmodel.dart';
import 'package:track_med_rec/ui/widgets/custom_appbar.dart';

class ViewTesting extends StatefulWidget {
  static const String id = '/SubscriptionServiceView';
  const ViewTesting({super.key, this.url, this.title, this.onTap});
  final String? url;
  final String? title;
  final void Function()? onTap;

  @override
  State<ViewTesting> createState() => _ViewTestingState();
}

class _ViewTestingState extends State<ViewTesting> {
  late final WebViewController controller;

  @override
  void initState() {
    super.initState();
    controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..loadRequest(Uri.parse(widget.url ?? ''));
  }

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<ViewTestingModel>.reactive(
        viewModelBuilder: () => ViewTestingModel(),
        builder: (context, model, child) => Scaffold(
              appBar: CustomAppBar(
                onTap: widget.onTap,
                toolBarHeight: 50,
                title: Text(
                  widget.title ?? '',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontSize: 22,
                        color: const Color(0xFF201A19).withOpacity(0.8),
                      ),
                ),
              ),
              body: SafeArea(
                child: IndexedStack(
                  index: model.stackToView,
                  children: [
                    WebViewWidget(controller: controller),
                    const Center(
                      child: CircularProgressIndicator(),
                    ),
                  ],
                ),
              ),
            ));
  }
}
