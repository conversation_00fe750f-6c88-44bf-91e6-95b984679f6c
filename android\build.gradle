buildscript {
    ext.kotlin_version = '1.9.21'
    repositories {
        google()
        mavenCentral()
        maven { url 'https://jcenter.bintray.com' }
        maven { url 'https://repo1.maven.org/maven2' }
        maven { url 'https://maven.aliyun.com/repository/public' }
        maven { url 'https://maven.aliyun.com/repository/google' }
        gradlePluginPortal()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:7.4.2'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath 'com.google.gms:google-services:4.4.0'
        classpath 'com.google.firebase:firebase-crashlytics-gradle:2.7.1'
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        maven { url 'https://jcenter.bintray.com' }
        maven { url 'https://repo1.maven.org/maven2' }
        maven { url 'https://maven.google.com' }
        maven { url 'https://maven.aliyun.com/repository/public' }
        maven { url 'https://maven.aliyun.com/repository/google' }
        gradlePluginPortal()
    }
    configurations.all {
        exclude group: "io.agora.rtc", module: "full-screen-sharing"
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
    afterEvaluate { project ->
       if (project.hasProperty('android')) {
           project.android {
               if (namespace == null) {
                   namespace project.group
               }
           }
       }
   }
}
subprojects {
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
