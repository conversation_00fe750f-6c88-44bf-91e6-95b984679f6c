name: track_med_rec
description: A new Flutter project.
publish_to: 'none'
version: 1.1.3+40

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  another_flushbar: ^1.12.30
  change_app_package_name: ^1.1.0
  clipboard: ^0.1.3
  country_state_city: ^0.1.6
  firebase_core: ^3.10.1
  firebase_messaging: ^15.2.1
  flutter:
    sdk: flutter
  flutter_hooks: ^0.20.1
  flutter_image_compress: ^2.1.0
  flutter_local_notifications: ^18.0.1
  flutter_screenutil: ^5.9.0
  flutter_secure_storage: ^9.0.0
  flutter_svg: ^2.0.17
  google_fonts: ^6.2.1
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  http: ^1.1.0
  image: ^4.1.3
  iconsax_flutter: ^1.0.0
  url_launcher: ^6.1.14
  image_picker: ^1.0.4
  intl: ^0.19.0
  intl_phone_number_input: ^0.7.3+1
  google_maps_flutter: ^2.5.3
  location: ^8.0.0
  local_auth: ^2.1.7
  qr_flutter: ^4.1.0
  rename_app: ^1.3.1
  stacked: ^3.4.0
  stacked_services: ^1.1.0
  shared_preferences: ^2.2.2
  go_router: ^14.6.3
  get_it: ^8.0.3
#  adoption:
#    path: /Users/<USER>/StudioProjects/adoption
  smooth_page_indicator: ^1.1.0
  agora_rtc_engine: ^6.2.6
  permission_handler: ^11.0.1
  draggable_widget: ^2.0.0
  screenshot: ^3.0.0
  path_provider: ^2.1.2
  dio: ^5.4.2+1
  wakelock_plus: ^1.1.2
  socket_io_client: ^3.0.2
  # Flutter package for creating shimmering loading effects
  shimmer: ^3.0.0
  firebase_crashlytics: ^4.3.1
  pdf: ^3.11.0
  printing: ^5.12.0
  geolocator: ^13.0.2
  file_picker: ^8.0.0+1
  geocoding: ^3.0.0
  iconsax: ^0.0.8
  ficonsax: ^0.0.3
  carousel_slider: ^5.0.0
  qr_code_scanner_plus: ^2.0.8+1

dev_dependencies:
  build_runner: ^2.4.5
  stacked_generator: ^1.3.3
  flutter_launcher_icons: ^0.14.3
  flutter_test:
    sdk: flutter

  flutter_lints: ^5.0.0
  mockito: ^5.4.1
  hive_generator: ^2.0.0
  ming_cute_icons: ^0.0.1
  country_code_picker: ^3.0.0

  pin_code_fields: ^8.0.1
  flutter_pin_code_fields: ^2.2.0
  otp_text_field: ^1.1.3
  iconify_flutter: ^0.0.5
  introduction_screen: ^3.1.11
  lottie: ^3.3.0
  dotted_line: ^3.2.2
  flutter_slidable: ^3.0.0
  webview_flutter: ^4.4.2
flutter_launcher_icons:
  ios: true
  image_path: "assets/icon.jpg"

flutter:
  uses-material-design: true


  assets:
    - assets/pngs/
    - assets/svgs/
    - assets/fonts/
    - assets/config/
    - assets/json/

  fonts:
    - family: Montserrat
      fonts:
        - asset: fonts/Montserrat-Medium.ttf
          weight: 300
        - asset: fonts/Montserrat-Regular.ttf
        - asset: fonts/Montserrat-VariableFont_wght.ttf
    - family: Jost
      fonts:
        - asset: fonts/Jost-Italic-VariableFont_wght.ttf
        - asset: fonts/Jost-VariableFont_wght.ttf

